@import "@radix-ui/themes/styles.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式 */
* {
  box-sizing: border-box;
  font-size: 13px;
}

body,
#root {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  color: var(--vscode-foreground);
  background: var(--vscode-editor-background);
  margin: 0;
  padding: 0;
  height: 100vh;
  overflow: hidden;
}

/* 应用容器 */
.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  min-width: 200px;
}

/* 内容容器 */
.content-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.page-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.page-content.active {
  display: block;
}

/* 聊天界面样式 */
.chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
  margin-bottom: 2px;
  position: relative;
}

.messages-container::-webkit-scrollbar {
  width: 4px;
}

.messages-container::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb {
  background: var(--vscode-scrollbarSlider-background);
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: var(--vscode-scrollbarSlider-hoverBackground);
}

/* 消息样式 */
.message {
  margin-bottom: 16px;
  padding: 0;
}

.message strong {
  display: inline-block;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 6px;
  color: var(--vscode-textLink-foreground);
}

.message-content {
  background: var(--vscode-editor-background);
  padding: 12px 16px;
  border-radius: 8px;
  border-left: 3px solid var(--vscode-progressBar-background);
  line-height: 1.5;
  word-wrap: break-word;
}

.message.error .message-content {
  background: var(--vscode-inputValidation-errorBackground);
  border-left-color: var(--vscode-inputValidation-errorBorder);
  color: var(--vscode-inputValidation-errorForeground);
}

/* 重新设计的输入区域样式 */
.input-container {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin: 4px 4px 12px;
  padding: 8px;
  border-radius: 8px;
  border: 1px solid var(--divider-background);
}

/* 发送按钮样式 */
.send-button {
  background: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  border: none;
  border-radius: 6px;
  padding: 0;
  font-size: clamp(10px, 2vw, 12px);
  font-weight: 600;
  cursor: pointer;
  padding: 0 12px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-button:hover {
  background: var(--vscode-button-hoverBackground);
}

.send-button:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: var(--vscode-descriptionForeground);
}

.empty-state-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-state-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--vscode-foreground);
}

.empty-state-description {
  font-size: 14px;
  opacity: 0.8;
  max-width: 300px;
  line-height: 1.5;
}

/* 工具提示样式 */
.tooltip {
  position: relative;
}

.tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--vscode-editorHoverWidget-background);
  color: var(--vscode-editorHoverWidget-foreground);
  border: 1px solid var(--vscode-editorHoverWidget-border);
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  z-index: 1000;
}

.tooltip:hover::after {
  opacity: 1;
}

/* 加载状态 */
.loading-dots {
  display: flex;
  gap: 2px;
}

.loading-dot {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: var(--vscode-progressBar-background);
}

/* 输入行样式 */
.input-row {
  display: flex;
  flex-direction: column;
  gap: 8px;
  position: relative;
}

.input-row:focus-within {
  border-color: var(--vscode-focusBorder);
}

/* 控制行样式 - mode-selector和send-button的容器 */
.controls-row {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: space-between;
  margin-top: 4px;
}

/* 模式选择器样式 */
.mode-selector {
  flex-shrink: 0;
  position: relative;
}

.mode-select {
  padding: 2px 6px;
  border-radius: 6px;
  background: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  font-size: clamp(10px, 2vw, 12px);
  font-weight: 500;
  font-family: inherit;
  cursor: pointer;
  height: 24px;
  appearance: none;
  outline: none;
}

.mode-select:hover {
  background: var(--vscode-button-hoverBackground);
}

.mode-select:focus {
  outline: none;
}

/* 模型类型选择器样式 */
.model-selector {
  flex-shrink: 0;
  position: relative;
  margin-left: auto;
  height: auto;
  font-size: clamp(10px, 2.5vw, 12px);
  line-height: 1.8;
  padding: 2px 6px;
  color: var(--text-link-decoration);
  border-radius: 4px;
  border: 1px solid var(--divider-background);
}

@media (max-width: 248px) {
  .model-selector {
    display: none;
  }
}

.model-select {
  padding: 2px 6px;
  border: 1px solid var(--vscode-input-border);
  border-radius: 6px;
  background: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
  font-size: 12px;
  font-weight: 500;
  font-family: inherit;
  cursor: pointer;
  height: 24px;
  appearance: none;
  min-width: 120px;
}

.model-select:hover {
  background: var(--vscode-button-secondaryHoverBackground);
}

/* 输入包装器样式 */
.input-wrapper {
  width: 100%;
  display: flex;
  position: relative;
}

/* 更新应用容器样式 */
.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--vscode-editor-background);
}

/* 辅助类 */
.hidden {
  display: none !important;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.info-row {
  display: flex;
  justify-content: space-between;
}

/* 输入增强功能样式 */
.input-enhancement {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  align-items: center;
  font-size: 11px;
  color: var(--vscode-descriptionForeground);
}

.character-count {
  font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace;
  padding: 2px 6px;
  background: var(--vscode-badge-background);
  color: var(--vscode-badge-foreground);
  border-radius: 4px;
  font-size: clamp(8px, 2.5vw, 10px);
}

.character-count.warning {
  background: var(--vscode-editorWarning-foreground);
  color: var(--vscode-editor-background);
}

.character-count.error {
  background: var(--vscode-editorError-foreground);
  color: var(--vscode-editor-background);
}

.character-count.hidden {
  visibility: hidden;
  display: block !important;
}

.keyboard-hint {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: clamp(8px, 2.5vw, 10px);
  opacity: 0.7;
}

.keyboard-hint kbd {
  background: var(--vscode-keybindingLabel-background);
  color: var(--vscode-keybindingLabel-foreground);
  border: 1px solid var(--vscode-keybindingLabel-border);
  border-radius: 3px;
  padding: 1px 4px;
  font-size: clamp(8px, 2.5vw, 10px);
  font-family: inherit;
  min-width: 16px;
  text-align: center;
}

/* 加载状态样式 */
.input-container.loading .send-button {
  pointer-events: none;
  position: relative;
}

.input-container.loading .send-button::after {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  border: 1px solid transparent;
  border-top: 1px solid currentColor;
  border-radius: 50%;
}

.input-container.loading .send-button span {
  opacity: 0;
}

.button-primary {
  background: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  font-weight: 500;
  cursor: pointer;
  border: 0 !important;
  border-radius: 4px;
  height: 28px;
  padding: 0 8px;
}

.button-primary:hover {
  background: var(--vscode-button-hoverBackground);
}

.tab-item {
  border: 0 !important;
  color: var(--vscode-icube-colorDefaultText);
  background-color: transparent;
  border-radius: 4px;
  padding: 8px;
  cursor: pointer;
}

.tab-item:hover {
  background-color: var(--button-icon-hover-background);
}

.tab-active {
  background-color: var(--button-icon-hover-background);
}

/* 汉堡菜单按钮样式 */
.hamburger-menu-button {
  padding: 0;
  border: none;
  background: var(--vscode-input-background);
  color: var(--vscode-descriptionForeground);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 13px;
}

.hamburger-menu-button:hover {
  background: var(--vscode-button-secondaryHoverBackground);
}

.hamburger-menu-content .rt-BaseMenuViewport,
.hamburger-menu-sub-content .rt-BaseMenuViewport {
  padding: 4px;
}

.hamburger-menu-content .hamburger-menu-text,
.hamburger-menu-sub-content .hamburger-menu-text {
  font-size: 13px !important;
  height: var(--space-5) !important;
}

.settings-close-button {
  border: 0 !important;
  color: var(--vscode-icube-colorDefaultText);
  background-color: transparent;
  border-radius: 4px;
  padding: 8px;
  cursor: pointer;
}

.settings-close-button:hover {
  background-color: var(--button-icon-hover-background);
}

.tips-text {
  color: var(--vscode-icube-colorGrayText);
  margin: 8px 0 12px;
  word-break: break-word;
}

/** 消息框输入样式 */
.tiptap-editor {
  width: 100%;
  height: 100%;
  max-height: 120px;
  min-height: 40px;
  background: transparent;
  border: none;
  border-radius: 0;
  outline: none;
  color: var(--vscode-foreground);
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  padding: 0;
  margin: 0;
  overflow-y: auto;
}

.tiptap-editor::-webkit-scrollbar {
  width: 4px;
}

.tiptap-editor::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.tiptap-editor::-webkit-scrollbar-thumb {
  background: var(--vscode-scrollbarSlider-background);
  border-radius: 3px;
}

.tiptap-editor::-webkit-scrollbar-thumb:hover {
  background: var(--vscode-scrollbarSlider-hoverBackground);
}

.tiptap-editor p {
  margin: 0;
  padding: 0;
}

.tiptap-editor:focus {
  outline: none;
  box-shadow: none;
}

.tiptap-wrapper {
  width: 100%;
  height: 100%;
  background: transparent;
  overflow-y: auto;
}

/* Remove default Tiptap styles */
.ProseMirror {
  outline: none !important;
  border: none !important;
  background: transparent !important;
  padding: 0 !important;
  margin: 0 !important;
}

.ProseMirror p {
  margin: 0 !important;
  padding: 0 !important;
}

.ProseMirror:focus {
  outline: none !important;
  box-shadow: none !important;
}

.mention {
  background-color: var(--vscode-badge-background);
  color: var(--vscode-badge-foreground);
  border-radius: 4px;
  padding: 0.2em 0.4em;
}

.image-node-renderer,
.file-node-renderer {
  display: inline-flex;
  vertical-align: middle;
  margin: 0 4px;
}

.image-node::after,
.file-node::after {
  content: "\200B";
}

.image-node-renderer img {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

.file-node-renderer {
  padding: 1px 4px;
  margin: 0 2px;
  border-radius: 6px;
  background: var(--vscode-badge-background);
  align-items: center;
  font-size: 14px;
}

.ProseMirror-selectednode {
  background-color: rgba(187, 214, 251, 0.6) !important;
}

.is-editor-empty::before {
  content: attr(data-placeholder);
  float: left;
  pointer-events: none;
  height: 0;
  line-height: 15px;
  color: var(--vscode-input-placeholderForeground);
}

.sub-title {
  margin: 12px 0;
  font-weight: 500;
}

.text-area {
  padding: 4px;
  border-radius: 4px;
  border: 1px solid var(--divider-background);
  background: var(--background);
}

.text-area-no-resize {
  resize: none;
}

.diff-view-accept-reject-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid var(--divider-background);
  background: var(--vscode-editor-background);
}
