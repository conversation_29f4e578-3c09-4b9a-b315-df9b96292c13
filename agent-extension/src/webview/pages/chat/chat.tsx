import React, { use<PERSON>allback, useContext, useEffect, useRef, useState } from 'react';
import type { GlobalInfo, UserInput } from '../../../bam/namespaces/userinput';
import { GlobalInfoType, PromptItemType } from '../../../bam/namespaces/userinput';
import { CHAT_MODES } from '../../../common/constants/conversation-types';
import { DEFAULT_MODEL_TYPE } from '../../../common/constants/model-types';
import { WorkMode, DEFAULT_WORK_MODE } from '../../../common/services/d2c-chat/settings';
import { MessageList } from '../../components/message-list/message-list';
import { StatusBar } from '../../components/status-bar';
import { TipTapEditorRef, TiptapEditor } from '../../components/tiptap-editor';
import { editorValueToPromptValue } from '../../components/tiptap-editor/transformer';
import { StatusProvider, useStatus } from '../../contexts/status-context';
import { RpcContext } from '../../core/rpc/rpc-context';
import { MessageType, useConversationHistory } from '../../hooks/use-conversation-history';
import { DiffViewAcceptRejectButton } from './diff-view-accept-reject-button';
import { FigmaTokenModal } from './figma-token-modal';
import { HamburgerMenu } from './hamburger-menu';
import { SendHorizontal } from 'lucide-react';
import { Kbd, Tooltip } from '@radix-ui/themes';

const shortCuts = [
  {
    key: 'Enter',
    description: '发送消息',
  },
  {
    key: 'Shift + Enter',
    description: '换行',
  },
];

const getItemKey = (id: string) => {
  return `any_input_${id}`;
};

const ChatAppContent: React.FC = () => {
  const { activeStatus } = useStatus();
  const [charCount, setCharCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedModelType, setSelectedModelType] = useState<number>(DEFAULT_MODEL_TYPE);
  const [selectedBusiness, setSelectedBusiness] = useState<string>('');
  const [selectedMode, setSelectedMode] = useState<WorkMode>(DEFAULT_WORK_MODE);

  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const { messages, roundFinish, currentConversationType, currentConversationId, currentConversationStatus, ...args } =
    useConversationHistory();

  const rpcClient = useContext(RpcContext);

  const tiptapEditorRef = useRef<TipTapEditorRef>(null);

  // 根据当前会话类型获取模式配置
  const currentMode = CHAT_MODES.find((mode) => mode.page === currentConversationType)!;

  const getCharacterCountClass = () => {
    if (charCount > 2000) {
      return 'over-limit';
    }
    if (charCount > 1500) {
      return 'warning';
    }
    return '';
  };

  const getEmptyStateMessage = () => {
    return (
      <div className="empty-state">
        <div className="empty-state-icon">{currentMode.icon}</div>
        <div className="empty-state-title">{currentMode.emptyStateTitle}</div>
        <div className="empty-state-description">{currentMode.emptyStateDescription}</div>
      </div>
    );
  };

  const sendMessage = async () => {
    if (!rpcClient || isLoading) return;
    setIsLoading(true);
    // 先偷个懒，现在getUserInfo不好搞
    rpcClient.call('loginAccount', {});

    const editorValue = await tiptapEditorRef.current?.getValue();

    if (!editorValue) {
      return;
    }

    const BlockNodeList = editorValue.content ?? [];
    if (BlockNodeList.length === 0) {
      return;
    }

    const hasContent = BlockNodeList.some((BlockNode) => {
      const inlineNodeList = BlockNode.content ?? [];
      return inlineNodeList.length > 0;
    });

    if (!hasContent) {
      return;
    }

    const globalInfos: GlobalInfo[] = [];

    const promptValue = await editorValueToPromptValue(editorValue, async (node) => {
      switch (node.type) {
        case 'file': {
          const { attrs } = node;
          const { path, name, type } = attrs;

          if (type === 'file') {
            const { content } = await rpcClient!.call<{ path: string }, { content: string }>(
              'workspace.readFileContent',
              {
                path,
              },
            );

            globalInfos.push({
              type: GlobalInfoType.File,
              file: {
                path,
                content,
                name,
              },
            });

            return {
              type: PromptItemType.File,
              file: {
                path,
              },
            };
          }
          const { content } = await rpcClient!.call<{ path: string }, { content: string }>(
            'workspace.readFolderListContent',
            {
              path,
            },
          );

          globalInfos.push({
            type: GlobalInfoType.Folder,
            folder: {
              path,
              content,
              name,
            },
          });

          return {
            type: PromptItemType.Folder,
            folder: {
              path,
            },
          };
        }

        case 'text': {
          return {
            type: PromptItemType.Text,
            text: {
              text: node.text,
            },
          };
        }

        case 'hardBreak': {
          return {
            type: PromptItemType.Text,
            text: {
              text: '\n',
            },
          };
        }
      }
    });

    const userInput: UserInput = {
      prompt: promptValue,
      global_infos: globalInfos,
    };

    tiptapEditorRef.current?.clearValue();
    refreshCharCount();

    // 如果当前会话不存在，则创建会话
    let cid = currentConversationId;
    if (!cid) {
      cid = await rpcClient?.call(currentMode.createConversationCommand, {});
      // 如果创建会话失败，则不发送消息
      if (!cid) return;
    }

    // 提示用户选择业务线
    if (!selectedBusiness) {
      rpcClient!.call('showInformationMessage', { content: '请选择业务线', modal: true });
      return;
    }

    if (!selectedMode) {
      rpcClient!.call('showInformationMessage', { content: '请选择工作模式', modal: true });
      return;
    }

    // 根据当前模式发送消息，并传递选择的模型类型
    rpcClient?.call(currentMode.sendMessageCommand, {
      cid: cid,
      text: userInput,
      model_type: selectedModelType,
      messageType: MessageType.MultiPart,
    });

    // appendMessage(
    //   buildMessage({
    //     sender: 'user',
    //     id: generateUuid(),
    //     type: MessageType.MultiPart,
    //     role: Role.User,
    //     content: userInput,
    //   }),
    // );

    await new Promise((resolve) => {
      setTimeout(() => resolve(undefined), 1000); // 模拟加载状态
    });
  };

  const refreshCharCount = () => {
    const charCount = tiptapEditorRef.current?.getCharCount();
    setCharCount(charCount ?? 0);
  };

  const handleChange = () => {
    tiptapEditorRef.current?.getValue().then((value) => {
      localStorage.setItem(getItemKey(currentConversationId), JSON.stringify(value));
    });

    refreshCharCount();
  };

  useEffect(() => {
    setIsLoading(!roundFinish);
  }, [roundFinish]);
  // useEffect(() => {
  //   const handleRoundFinish = () => {
  //     setIsLoading(false);
  //   };
  //   rpcClient?.on('roundFinish', handleRoundFinish);
  //   return () => {
  //     rpcClient?.off('roundFinish', handleRoundFinish);
  //   };
  // }, [rpcClient]);

  useEffect(() => {
    const value = localStorage.getItem(getItemKey(currentConversationId));

    if (value) {
      tiptapEditorRef.current?.setValue(JSON.parse(value));
    }
  }, [currentConversationId]);

  // 处理键盘事件：Enter 发送，Shift+Enter 换行
  const handleEditorKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (!isLoading) {
        sendMessage();
      }
    }
    // Shift+Enter 默认行为即为换行，无需处理
  };

  return (
    <section className="chat-container">
      <div
        ref={messagesContainerRef}
        className={`messages-container ${activeStatus ? 'with-status-bar' : ''}`}
        style={{
          // 当状态栏显示时，减少高度避免遮挡
          marginBottom: activeStatus ? '52px' : '0',
        }}
      >
        {messages.length === 0 && getEmptyStateMessage()}
        {messages.length > 0 && <MessageList messages={messages} messagesContainerRef={messagesContainerRef} />}
      </div>
      <div className={`input-container ${isLoading ? 'loading' : ''}`}>
        <div style={{ position: 'absolute', width: '100%', top: -4, left: 0, transform: 'translateY(-100%)' }}>
          <DiffViewAcceptRejectButton />
          <StatusBar />
        </div>
        <div className="input-row">
          <div className="input-wrapper">
            <TiptapEditor
              ref={tiptapEditorRef}
              placeholder={currentMode.placeholder}
              onChange={handleChange}
              onKeyDown={handleEditorKeyDown}
            />
          </div>

          <div className="info-row pt-2">
            <div className="input-enhancement">
              <div className={`character-count ${getCharacterCountClass()}`}>{charCount}/2000</div>
            </div>

            <div className="controls-row cursor-pointer">
              {/* 汉堡菜单 */}
              <HamburgerMenu
                currentConversationId={currentConversationId}
                onBusinessChange={setSelectedBusiness}
                onModeChange={setSelectedMode}
              />
              <Tooltip
                content={
                  <div className="flex flex-col space-y-2">
                    {shortCuts.map((shortcut) => (
                      <div key={shortcut.key} className="flex items-center gap-2">
                        <Kbd>{shortcut.key}</Kbd>
                        <span>{shortcut.description}</span>
                      </div>
                    ))}
                  </div>
                }
              >
                <SendHorizontal
                  color={isLoading ? '#ccc' : undefined}
                  className={isLoading ? 'cursor-not-allowed  opacity-50' : ''}
                  size={16}
                  onClick={sendMessage}
                />
              </Tooltip>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

const chatApp: React.FC = () => {
  return (
    <StatusProvider>
      <ChatAppContent />
      <FigmaTokenModal />
    </StatusProvider>
  );
};

export { chatApp as ChatApp };
