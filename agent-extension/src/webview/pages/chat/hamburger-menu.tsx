import React, { useCallback, useContext, useEffect, useState } from 'react';
import { Menu } from 'lucide-react';
import { DropdownMenu } from '@radix-ui/themes';
import { RpcContext } from '../../core/rpc/rpc-context';
import { WorkMode, DEFAULT_WORK_MODE } from '../../../common/services/d2c-chat/settings';

interface HamburgerMenuProps {
  currentConversationId: string;
  onBusinessChange?: (business: string) => void;
  onModeChange?: (mode: WorkMode) => void;
}

export const HamburgerMenu: React.FC<HamburgerMenuProps> = ({
  currentConversationId,
  onBusinessChange,
  onModeChange,
}) => {
  const [businessList, setBusinessList] = useState<string[]>([]);
  const [selectedBusiness, setSelectedBusiness] = useState<string>('');
  const [isLoadingBusinessList, setIsLoadingBusinessList] = useState(false);
  const [isChangingBusiness, setIsChangingBusiness] = useState(false);

  // 模式相关状态
  const [selectedMode, setSelectedMode] = useState<WorkMode>(DEFAULT_WORK_MODE);
  const [isChangingMode, setIsChangingMode] = useState(false);

  const rpcClient = useContext(RpcContext);

  const setConversationBusiness = useCallback(
    async (business: string) => {
      try {
        setIsChangingBusiness(true);
        setSelectedBusiness(business);
        if (!rpcClient || !currentConversationId) return;
        await rpcClient.call('setConversationBusiness', { cid: currentConversationId, business });
        // 通知父组件业务选择已更改
        onBusinessChange?.(business);
      } finally {
        setIsChangingBusiness(false);
      }
    },
    [rpcClient, currentConversationId, onBusinessChange],
  );

  const setConversationMode = useCallback(
    async (mode: WorkMode) => {
      try {
        setIsChangingMode(true);
        setSelectedMode(mode);
        if (!rpcClient || !currentConversationId) return;
        await rpcClient.call('setConversationMode', { cid: currentConversationId, mode });
        // 通知父组件模式选择已更改
        onModeChange?.(mode);
      } finally {
        setIsChangingMode(false);
      }
    },
    [rpcClient, currentConversationId, onModeChange],
  );

  // 获取 business 列表
  useEffect(() => {
    console.info('getConversationBusinessList', currentConversationId);
    if (!rpcClient) return;

    const fetchBusinessList = async () => {
      try {
        setIsLoadingBusinessList(true);
        const list: string[] = await rpcClient.call('getConversationBusinessList', {});
        setBusinessList(list);

        if (!currentConversationId) {
          await setConversationBusiness(list[0]);
          return;
        }

        const business: string = await rpcClient.call('getConversationBusiness', { cid: currentConversationId });
        await setConversationBusiness(business || list[0]);
      } finally {
        setIsLoadingBusinessList(false);
      }
    };

    fetchBusinessList();
  }, [rpcClient, currentConversationId, setConversationBusiness]);

  // 获取当前模式
  useEffect(() => {
    console.info('getConversationMode', currentConversationId);
    if (!rpcClient) return;

    const fetchCurrentMode = async () => {
      try {
        if (!currentConversationId) {
          await setConversationMode(DEFAULT_WORK_MODE);
          return;
        }

        const mode: WorkMode = await rpcClient.call('getConversationMode', { cid: currentConversationId });
        await setConversationMode(mode || DEFAULT_WORK_MODE);
      } catch (error) {
        console.error('Failed to fetch conversation mode:', error);
        await setConversationMode(DEFAULT_WORK_MODE);
      }
    };

    fetchCurrentMode();
  }, [rpcClient, currentConversationId, setConversationMode]);

  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger>
        <div className="hamburger-menu-button" title="菜单">
          <Menu size={14} />
        </div>
      </DropdownMenu.Trigger>
      <DropdownMenu.Content className="hamburger-menu-content">
        <DropdownMenu.Sub>
          <DropdownMenu.SubTrigger className="hamburger-menu-text">业务</DropdownMenu.SubTrigger>
          <DropdownMenu.SubContent className="hamburger-menu-sub-content">
            {isLoadingBusinessList ? (
              <DropdownMenu.Item className="hamburger-menu-text" disabled>
                加载中...
              </DropdownMenu.Item>
            ) : (
              businessList.map((business) => (
                <DropdownMenu.Item
                  className="hamburger-menu-text"
                  key={business}
                  onSelect={() => setConversationBusiness(business)}
                  disabled={isChangingBusiness}
                >
                  {selectedBusiness === business ? '✓ ' : ''}
                  {isChangingBusiness && business === selectedBusiness ? '切换中...' : business}
                </DropdownMenu.Item>
              ))
            )}
          </DropdownMenu.SubContent>
        </DropdownMenu.Sub>
        <DropdownMenu.Sub>
          <DropdownMenu.SubTrigger className="hamburger-menu-text">模式</DropdownMenu.SubTrigger>
          <DropdownMenu.SubContent className="hamburger-menu-sub-content">
            {Object.values(WorkMode).map((mode) => (
              <DropdownMenu.Item
                className="hamburger-menu-text"
                key={mode}
                onSelect={() => setConversationMode(mode)}
                disabled={isChangingMode}
              >
                {selectedMode === mode ? '✓ ' : ''}
                {isChangingMode && mode === selectedMode ? '切换中...' : mode}
              </DropdownMenu.Item>
            ))}
          </DropdownMenu.SubContent>
        </DropdownMenu.Sub>
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  );
};
