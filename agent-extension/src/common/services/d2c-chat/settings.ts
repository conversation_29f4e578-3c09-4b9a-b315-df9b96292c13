export interface ID2CSettings {
  figma_token: string | undefined;
}

// 模式枚举
export enum WorkMode {
  PlanAct = 'PlanAct',
  Act = 'Act',
}

// 默认工作模式
export const DEFAULT_WORK_MODE = WorkMode.PlanAct;

export const D2C_SETTINGS_STORAGE_KEY = 'codin_d2c_settings';
export const PLANNING_MODEL_KEY = 'planning_model';
export const CODING_MODEL_KEY = 'coding_model';
export const UNDERSTANDING_MODEL_KEY = 'understanding_model';
export const WORK_MODE_KEY = 'work_mode';
