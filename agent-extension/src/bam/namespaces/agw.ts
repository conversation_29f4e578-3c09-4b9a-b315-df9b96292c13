// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as agentserver from "./agentserver";
import * as benchmark from "./benchmark";
import * as coding from "./coding";
import * as codesearch from "./codesearch";
import * as d2c from "./d2c";
import * as prd from "./prd";
import * as base from "./base";
import * as asset from "./asset";
import * as conversation from "./conversation";

export type Int64 = string | number;

export interface AskBenchmarkAgentRequest {
  params: agentserver.AgentInferenceParams;
}

export interface AskBenchmarkAgentResponse {
  code: number;
  message: string;
  log_id: string;
  data?: benchmark.BenchmarkResponse;
}

export interface AskCodeAgentRequest {
  params: agentserver.AgentInferenceParams;
}

export interface AskCodeAgentResponse {
  code: number;
  message: string;
  log_id: string;
  data?: coding.CodingResponse;
}

export interface AskCodeReviewAgentRequest {
  params: agentserver.AgentInferenceParams;
}

export interface AskCodeReviewAgentResponse {
  code: number;
  message: string;
  log_id: string;
  data?: coding.CodeReviewResponse;
}

export interface AskCodeSearchAgentRequest {
  params: agentserver.AgentInferenceParams;
}

export interface AskCodeSearchAgentResponse {
  code: number;
  message: string;
  log_id: string;
  data?: codesearch.GetCodeSearchResponse;
}

export interface AskD2cAgentRequest {
  params: agentserver.AgentInferenceParams;
}

export interface AskD2cAgentResponse {
  code: number;
  message: string;
  log_id: string;
  data?: d2c.DesignToCodeResponse;
}

export interface AskPlanAgentRequest {
  params: agentserver.AgentInferenceParams;
}

export interface AskPlanAgentResponse {
  code: number;
  message: string;
  log_id: string;
  data?: prd.PlanningResponse;
}

export interface AskUnderstandAgentRequest {
  params: agentserver.AgentInferenceParams;
}

export interface AskUnderstandAgentResponse {
  code: number;
  message: string;
  log_id: string;
  data?: prd.UnderstandingResponse;
}

export interface BatchCreateBenchmarkTaskData {
  result_list: Array<benchmark.BatchCreateTaskResult>;
}

export interface BatchCreateBenchmarkTaskRequest {
  /** 批量任务列表 */
  task_list: Array<benchmark.BatchCreateTaskItem>;
  model_type?: base.ModelType;
}

export interface BatchCreateBenchmarkTaskResponse {
  code: number;
  message: string;
  log_id: string;
  data?: BatchCreateBenchmarkTaskData;
}

export interface CalcConversationTokensRequest {
  conversation_ids: Array<string>;
}

export interface CalcConversationTokensResponse {
  code: number;
  message: string;
  log_id: string;
  data?: asset.CalcConversationTokensResponse;
}

export interface CancelConversationRequest {
  /** 会话id */
  id: string;
}

export interface CancelConversationResponse {
  code: number;
  message: string;
  log_id: string;
  data?: asset.CancelConversationResponse;
}

export interface CreateBenchmarkTaskRequest {
  /** 需要支持多仓定义 */
  changes: Array<benchmark.Change>;
  /** RFC描述 */
  rfc: string;
  model_type?: base.ModelType;
}

export interface CreateBenchmarkTaskResponse {
  code: number;
  message: string;
  log_id: string;
  data?: benchmark.CreateBenchmarkTaskResponse;
}

export interface CreateConversationRequest {
  /** 会话类型 */
  type: conversation.ConversationType;
  /** 父会话id */
  parent_id?: string;
  /** 父会话版本 */
  parent_version?: Int64;
}

export interface CreateConversationResponse {
  code: number;
  message: string;
  log_id: string;
  data?: asset.CreateConversationResponse;
}

export interface GetBenchmarkReportListRequest {
  conversation_ids: Array<string>;
}

export interface GetBenchmarkReportListResponse {
  code: number;
  message: string;
  log_id: string;
  data?: benchmark.GetReportResponse;
}

export interface GetConversationListRequest {
  /** 开始时间 */
  StartTime?: Int64;
  /** 结束时间 */
  EndTime?: Int64;
}

export interface GetConversationListResponse {
  code: number;
  message: string;
  log_id: string;
  data?: asset.GetConversationListResponse;
}

export interface GetConversationRequest {
  /** 会话id */
  Id: string;
  /** 是否需要message */
  Message?: boolean;
}

export interface GetConversationResponse {
  code: number;
  message: string;
  log_id: string;
  data?: asset.GetConversationResponse;
}

export interface GetStreamRangeRequest {
  /** 会话id */
  Id: string;
  /** 开始版本 */
  Start: Int64;
  /** 结束版本 */
  End: Int64;
}

export interface GetStreamRangeResponse {
  code: number;
  message: string;
  log_id: string;
  data?: asset.GetStreamRangeResponse;
}

export interface RetryBenchmarkAgentRequest {
  params: agentserver.AgentRetryParams;
}

export interface RetryBenchmarkAgentResponse {
  code: number;
  message: string;
  log_id: string;
  data?: benchmark.RetryBenchmarkResponse;
}

export interface RetryCodeAgentRequest {
  params: agentserver.AgentRetryParams;
}

export interface RetryCodeAgentResponse {
  code: number;
  message: string;
  log_id: string;
  data?: coding.RetryCodingResponse;
}

export interface RetryCodeSearchAgentRequest {
  params: agentserver.AgentRetryParams;
}

export interface RetryCodeSearchAgentResponse {
  code: number;
  message: string;
  log_id: string;
  data?: codesearch.RetryCodeSearchResponse;
}

export interface RetryD2cAgentRequest {
  params: agentserver.AgentRetryParams;
}

export interface RetryD2cAgentResponse {
  code: number;
  message: string;
  log_id: string;
  data?: d2c.RetryDesignToCodeResponse;
}

export interface RetryPlanAgentRequest {
  params: agentserver.AgentRetryParams;
}

export interface RetryPlanAgentResponse {
  code: number;
  message: string;
  log_id: string;
  data?: prd.RetryPlanningResponse;
}

export interface RetryUnderstandAgentRequest {
  params: agentserver.AgentRetryParams;
}

export interface RetryUnderstandAgentResponse {
  code: number;
  message: string;
  log_id: string;
  data?: prd.RetryUnderstandingResponse;
}
/* eslint-enable */
